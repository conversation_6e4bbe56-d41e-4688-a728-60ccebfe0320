/* TailwindCSS imports */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #1e293b;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  min-height: 100vh;
  transition: all 0.5s ease;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(147, 197, 253, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
  transition: all 0.5s ease;
}

.dark body {
  color: #f1f5f9;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.dark body::before {
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(147, 197, 253, 0.04) 0%, transparent 50%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom utility classes */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.btn-primary {
  @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 dark:from-primary-500 dark:to-primary-600 dark:hover:from-primary-600 dark:hover:to-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-secondary-800 shadow-lg hover:shadow-xl transform hover:scale-105 relative overflow-hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }
}

.btn-secondary {
  @apply bg-gradient-to-r from-secondary-100 to-secondary-200 hover:from-secondary-200 hover:to-secondary-300 dark:from-secondary-700 dark:to-secondary-600 dark:hover:from-secondary-600 dark:hover:to-secondary-500 text-secondary-800 dark:text-secondary-200 font-medium py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2 dark:focus:ring-offset-secondary-800 shadow-md hover:shadow-lg transform hover:scale-105;
}

.btn-outline {
  @apply border-2 border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400 hover:bg-primary-600 dark:hover:bg-primary-500 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-secondary-800 shadow-md hover:shadow-lg transform hover:scale-105 relative overflow-hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.1));
    transition: width 0.3s ease;
    z-index: -1;
  }

  &:hover::before {
    width: 100%;
  }
}

.card {
  @apply bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-xl shadow-lg border border-secondary-200/50 dark:border-secondary-700/50 p-6 transition-all duration-500 hover:shadow-2xl dark:hover:shadow-2xl hover:scale-105 relative overflow-hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  &:hover::before {
    opacity: 1;
  }

  &:hover {
    border-color: rgba(37, 99, 235, 0.3);
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(37, 99, 235, 0.05);
  }

  .dark &:hover {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.3),
      0 10px 10px -5px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(59, 130, 246, 0.1);
  }
}

.section-padding {
  @apply py-16 lg:py-24;
}

.text-gradient {
  @apply bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent;
}

/* Animation utilities */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.animate {
  opacity: 1;
  transform: translateY(0);
}

.stagger-animation {
  animation-delay: var(--animation-delay, 0s);
}

/* Enhanced Animation Classes as per rules.md */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-30px);
  }
  70% {
    transform: translateY(-15px);
  }
  90% {
    transform: translateY(-4px);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes wobble {
  0% {
    transform: translateX(0%);
  }
  15% {
    transform: translateX(-25%) rotate(-5deg);
  }
  30% {
    transform: translateX(20%) rotate(3deg);
  }
  45% {
    transform: translateX(-15%) rotate(-3deg);
  }
  60% {
    transform: translateX(10%) rotate(2deg);
  }
  75% {
    transform: translateX(-5%) rotate(-1deg);
  }
  100% {
    transform: translateX(0%);
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn 300ms ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 400ms ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 400ms ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 400ms ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 400ms ease-out;
}

.animate-slide-up {
  animation: slideUp 400ms ease-out;
}

.animate-bounce-in {
  animation: bounceIn 500ms ease-out;
}

.animate-scale-in {
  animation: scaleIn 300ms ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-shake {
  animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite both;
}

.animate-wobble {
  animation: wobble 1s ease-in-out both;
}

/* Hover animations */
.hover-lift {
  @apply transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

.hover-glow {
  @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/25;
}

.hover-scale {
  @apply transition-transform duration-300 hover:scale-110;
}

/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Glass morphism effect */
.glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl;
}

.dark .glass {
  @apply bg-black/10 border-white/10;
}

/* Enhanced Form Styling */
.form-input {
  @apply w-full px-4 py-3 border border-secondary-300 dark:border-secondary-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-secondary-700 text-secondary-900 dark:text-white transition-all duration-300 shadow-sm hover:shadow-md;

  /* Ensure proper contrast and visibility */
  color: #1e293b !important;
  background-color: rgba(255, 255, 255, 0.95) !important;

  &::placeholder {
    color: #64748b !important;
    opacity: 0.7;
  }

  &:focus {
    transform: translateY(-1px);
    background-color: rgba(255, 255, 255, 1) !important;
    color: #1e293b !important;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  /* Dark mode styling */
  .dark & {
    color: #f1f5f9 !important;
    background-color: rgba(30, 41, 59, 0.95) !important;

    &::placeholder {
      color: #94a3b8 !important;
      opacity: 0.8;
    }

    &:focus {
      background-color: rgba(30, 41, 59, 1) !important;
      color: #f1f5f9 !important;
      box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.3),
        0 2px 4px -1px rgba(0, 0, 0, 0.2),
        0 0 0 3px rgba(59, 130, 246, 0.2);
    }
  }
}

.form-label {
  @apply block text-sm font-medium mb-2 transition-colors duration-300;
  color: #374151 !important;
  font-weight: 600;

  .dark & {
    color: #d1d5db !important;
  }
}

/* Enhanced text visibility for contact form */
.contact-form-heading {
  color: #1f2937 !important;

  .dark & {
    color: #f9fafb !important;
  }
}

.contact-form-text {
  color: #4b5563 !important;

  .dark & {
    color: #d1d5db !important;
  }
}

/* Ensure all section headings have proper contrast */
.section-heading {
  color: #1f2937 !important;

  .dark & {
    color: #f9fafb !important;
  }
}

.section-text {
  color: #4b5563 !important;

  .dark & {
    color: #d1d5db !important;
  }
}

/* Enhanced Section Backgrounds */
.section-light {
  @apply bg-gradient-to-br from-white/90 to-secondary-50/90 dark:from-secondary-900/90 dark:to-secondary-800/90 backdrop-blur-sm;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 10% 20%, rgba(37, 99, 235, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 90% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }

  .dark &::before {
    background:
      radial-gradient(circle at 10% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 90% 80%, rgba(37, 99, 235, 0.05) 0%, transparent 50%);
  }
}

.section-accent {
  @apply bg-gradient-to-br from-secondary-50/90 to-primary-50/90 dark:from-secondary-800/90 dark:to-secondary-700/90 backdrop-blur-sm;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 30% 40%, rgba(37, 99, 235, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 70% 60%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
}

/* Advanced Animation Classes */
.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 2s ease-in-out infinite;
}

.animate-wiggle {
  animation: wiggle 1s ease-in-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-zoom-in {
  animation: zoomIn 0.5s ease-out;
}

.animate-flip {
  animation: flip 0.6s ease-in-out;
}

/* New Keyframes */
@keyframes glow {
  from {
    text-shadow: 0 0 5px rgba(37, 99, 235, 0.5), 0 0 10px rgba(37, 99, 235, 0.3);
  }
  to {
    text-shadow: 0 0 10px rgba(37, 99, 235, 0.8), 0 0 20px rgba(37, 99, 235, 0.5);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes wiggle {
  0%, 7%, 14%, 21%, 28%, 35%, 42%, 49%, 56%, 63%, 70%, 77%, 84%, 91%, 98%, 100% {
    transform: rotate(0deg);
  }
  3.5%, 10.5%, 17.5%, 24.5%, 31.5%, 38.5%, 45.5%, 52.5%, 59.5%, 66.5%, 73.5%, 80.5%, 87.5%, 94.5% {
    transform: rotate(2deg);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes flip {
  from {
    transform: perspective(400px) rotateY(-90deg);
  }
  to {
    transform: perspective(400px) rotateY(0deg);
  }
}
