// Services component specific styles

.service-card {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
}

.service-icon {
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1) rotate(5deg);
  }
}

.feature-tag {
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.pricing-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .pricing-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

// Animation delays for staggered effects
@for $i from 1 through 6 {
  .service-card:nth-child(#{$i}) {
    animation-delay: #{0.1 + ($i - 1) * 0.1}s;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .service-card {
    margin-bottom: 2rem;
  }
  
  .service-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

// Enhanced hover effects for service cards
.service-card {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover::before {
    left: 100%;
  }
}

// Smooth transitions for all interactive elements
.btn-primary,
.btn-outline {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
  }
  
  &:hover::before {
    width: 300px;
    height: 300px;
  }
}
