// Service detail component specific styles

.breadcrumb {
  a {
    transition: color 0.2s ease;
    
    &:hover {
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

.feature-item {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateX(5px);
    background-color: rgba(37, 99, 235, 0.05);
  }
}

.benefit-item {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateX(5px);
  }
}

.process-step {
  position: relative;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateX(5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 16px;
    top: 100%;
    width: 2px;
    height: 16px;
    background: linear-gradient(to bottom, #3b82f6, transparent);
  }
}

.pricing-card {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(37, 99, 235, 0.02) 100%);
  border: 2px solid rgba(37, 99, 235, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(37, 99, 235, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(37, 99, 235, 0.1);
  }
}

.dark .pricing-card {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
  border-color: rgba(37, 99, 235, 0.2);
  
  &:hover {
    border-color: rgba(37, 99, 235, 0.3);
  }
}

.contact-card {
  background: linear-gradient(135deg, rgba(243, 115, 22, 0.05) 0%, rgba(243, 115, 22, 0.02) 100%);
  border: 2px solid rgba(243, 115, 22, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(243, 115, 22, 0.2);
    transform: translateY(-2px);
  }
}

.dark .contact-card {
  background: linear-gradient(135deg, rgba(243, 115, 22, 0.1) 0%, rgba(243, 115, 22, 0.05) 100%);
  border-color: rgba(243, 115, 22, 0.2);
  
  &:hover {
    border-color: rgba(243, 115, 22, 0.3);
  }
}

.related-service-card {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    
    .service-icon {
      transform: scale(1.1);
    }
  }
}

.service-icon {
  transition: transform 0.3s ease;
}

// Loading spinner
.loading-spinner {
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .process-step {
    &:not(:last-child)::after {
      display: none;
    }
  }
  
  .feature-item,
  .benefit-item,
  .process-step {
    &:hover {
      transform: none;
    }
  }
}

// Enhanced animations for mobile
@media (max-width: 640px) {
  .animate-fade-in-up {
    animation-duration: 0.6s;
  }
  
  .hover-lift:hover {
    transform: none;
  }
}
