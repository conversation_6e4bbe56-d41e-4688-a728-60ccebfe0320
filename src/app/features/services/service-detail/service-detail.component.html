<!-- Loading State -->
<div *ngIf="loading" class="min-h-screen flex items-center justify-center">
  <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
</div>

<!-- Service Detail Content -->
<div *ngIf="!loading && service">
  <!-- Hero Section -->
  <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 dark:from-primary-800 dark:via-primary-900 dark:to-secondary-900 text-white py-20 lg:py-32 transition-all duration-500">
    <div class="absolute inset-0 opacity-10">
      <div class="absolute inset-0 bg-white bg-opacity-5"></div>
    </div>

    <div class="container-custom relative z-10">
      <div class="max-w-4xl mx-auto text-center animate-fade-in-up">
        <!-- Breadcrumb -->
        <nav class="mb-8">
          <ol class="flex items-center justify-center space-x-2 text-primary-200">
            <li><a routerLink="/" class="hover:text-white transition-colors">Home</a></li>
            <li><span class="mx-2">/</span></li>
            <li><a routerLink="/services" class="hover:text-white transition-colors">Services</a></li>
            <li><span class="mx-2">/</span></li>
            <li class="text-white">{{ service.title }}</li>
          </ol>
        </nav>

        <!-- Service Icon -->
        <div class="w-24 h-24 mx-auto mb-6 bg-white/20 rounded-full flex items-center justify-center text-4xl backdrop-blur-sm border border-white/30">
          {{ service.icon }}
        </div>

        <h1 class="text-4xl lg:text-6xl font-bold mb-6">{{ service.title }}</h1>
        <p class="text-xl lg:text-2xl text-primary-100 max-w-3xl mx-auto mb-8">
          {{ service.description }}
        </p>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a routerLink="/contact" class="bg-white text-primary-600 hover:bg-primary-50 font-medium py-4 px-8 rounded-lg transition-colors duration-200 text-lg">
            Get Free Consultation
          </a>
          <a routerLink="/contact" class="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-4 px-8 rounded-lg transition-all duration-200 text-lg">
            Request Quote
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Service Details Section -->
  <section class="section-padding bg-white dark:bg-secondary-900 transition-colors duration-500">
    <div class="container-custom">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">

        <!-- Main Content -->
        <div class="lg:col-span-2">

          <!-- Features Section -->
          <div class="mb-12 animate-fade-in-up">
            <h2 class="text-3xl font-bold text-secondary-800 dark:text-white mb-6 transition-colors duration-300">
              What's Included
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div *ngFor="let feature of service.features; let i = index"
                   class="flex items-center p-4 bg-secondary-50 dark:bg-secondary-800 rounded-lg hover-lift animate-fade-in-up transition-colors duration-300"
                   [style.animation-delay]="(0.1 + i * 0.05) + 's'">
                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-secondary-700 dark:text-secondary-300 transition-colors duration-300">{{ feature }}</span>
              </div>
            </div>
          </div>

          <!-- Benefits Section -->
          <div class="mb-12 animate-fade-in-up" style="animation-delay: 0.2s;">
            <h2 class="text-3xl font-bold text-secondary-800 dark:text-white mb-6 transition-colors duration-300">
              Key Benefits
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div *ngFor="let benefit of service.benefits; let i = index"
                   class="flex items-start animate-fade-in-up"
                   [style.animation-delay]="(0.3 + i * 0.05) + 's'">
                <div class="w-6 h-6 bg-accent-500 rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-secondary-700 dark:text-secondary-300 transition-colors duration-300">{{ benefit }}</span>
              </div>
            </div>
          </div>

          <!-- Process Section -->
          <div class="animate-fade-in-up" style="animation-delay: 0.4s;">
            <h2 class="text-3xl font-bold text-secondary-800 dark:text-white mb-6 transition-colors duration-300">
              Our Process
            </h2>
            <div class="space-y-4">
              <div *ngFor="let step of service.process; let i = index"
                   class="flex items-start p-4 bg-secondary-50 dark:bg-secondary-800 rounded-lg hover-lift animate-fade-in-up transition-colors duration-300"
                   [style.animation-delay]="(0.5 + i * 0.1) + 's'">
                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-4 flex-shrink-0 text-white font-bold">
                  {{ i + 1 }}
                </div>
                <span class="text-secondary-700 dark:text-secondary-300 transition-colors duration-300">{{ step }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">

          <!-- Pricing Card -->
          <div *ngIf="service.pricing" class="card mb-8 animate-fade-in-up" style="animation-delay: 0.6s;">
            <div class="text-center">
              <h3 class="text-xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
                Pricing
              </h3>
              <div class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2 transition-colors duration-300">
                {{ service.pricing.starting }}
              </div>
              <p class="text-secondary-600 dark:text-secondary-300 mb-6 transition-colors duration-300">
                {{ service.pricing.description }}
              </p>
              <div class="space-y-3">
                <a routerLink="/contact" class="block w-full btn-primary text-center">
                  Get Started
                </a>
                <a routerLink="/contact" class="block w-full btn-outline text-center">
                  Get Custom Quote
                </a>
              </div>
            </div>
          </div>

          <!-- Contact Card -->
          <div class="card animate-fade-in-up" style="animation-delay: 0.7s;">
            <h3 class="text-xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
              Need Help?
            </h3>
            <p class="text-secondary-600 dark:text-secondary-300 mb-4 transition-colors duration-300">
              Our experts are ready to help you with {{ service.title.toLowerCase() }}. Get in touch for a free consultation.
            </p>
            <div class="space-y-3">
              <a href="tel:+91-9876543210" class="flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-300">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                </svg>
                +91-9876543210
              </a>
              <a href="mailto:info&#64;excellenceca.com" class="flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-300">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                </svg>
                info&#64;excellenceca.com
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Related Services Section -->
  <section class="section-padding bg-secondary-50 dark:bg-secondary-800 transition-colors duration-500">
    <div class="container-custom">
      <div class="text-center mb-12 animate-fade-in-up">
        <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
          Other Services
        </h2>
        <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-2xl mx-auto transition-colors duration-300">
          Explore our other professional services
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div *ngFor="let otherService of getOtherServices(); let i = index"
             class="card text-center hover-lift animate-fade-in-up"
             [style.animation-delay]="(0.2 + i * 0.1) + 's'">
          <div class="text-4xl mb-4">{{ otherService.icon }}</div>
          <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-3 transition-colors duration-300">
            {{ otherService.title }}
          </h3>
          <p class="text-secondary-600 dark:text-secondary-300 mb-4 transition-colors duration-300">
            {{ otherService.description.substring(0, 100) }}...
          </p>
          <a [routerLink]="otherService.route" class="btn-outline w-full text-center">
            Learn More
          </a>
        </div>
      </div>
    </div>
  </section>
</div>

<!-- Service Not Found -->
<div *ngIf="!loading && !service" class="min-h-screen flex items-center justify-center">
  <div class="text-center">
    <h1 class="text-4xl font-bold text-secondary-800 dark:text-white mb-4">Service Not Found</h1>
    <p class="text-secondary-600 dark:text-secondary-300 mb-8">The service you're looking for doesn't exist.</p>
    <a routerLink="/services" class="btn-primary">View All Services</a>
  </div>
</div>
