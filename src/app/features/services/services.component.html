<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 dark:from-primary-800 dark:via-primary-900 dark:to-secondary-900 text-white py-20 lg:py-32 transition-all duration-500">
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0 bg-white bg-opacity-5"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="text-center animate-fade-in-up">
      <h1 class="text-4xl lg:text-6xl font-bold mb-6">Our Professional Services</h1>
      <p class="text-xl lg:text-2xl text-primary-100 max-w-3xl mx-auto mb-8">
        Comprehensive chartered accountant services designed to help your business thrive and grow with expert financial guidance.
      </p>

      <!-- Quick Navigation -->
      <div class="flex flex-wrap justify-center gap-4 mt-8">
        <a *ngFor="let service of services"
           [routerLink]="service.route"
           class="px-6 py-3 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-all duration-300 hover:scale-105 backdrop-blur-sm border border-white/30">
          {{ service.title }}
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Services Overview Section -->
<section class="section-padding section-light transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Complete Financial Solutions
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        From tax planning to business advisory, we provide end-to-end financial services to support your business at every stage
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div *ngFor="let service of services; let i = index"
           class="card group hover-lift animate-fade-in-up"
           [style.animation-delay]="(0.2 + i * 0.1) + 's'">

        <!-- Service Icon -->
        <div class="text-center mb-6">
          <div class="w-20 h-20 mx-auto bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white text-3xl shadow-lg group-hover:scale-110 transition-transform duration-300">
            {{ service.icon }}
          </div>
        </div>

        <!-- Service Content -->
        <div class="text-center">
          <h3 class="text-xl font-bold text-secondary-800 dark:text-white mb-3 transition-colors duration-300">
            {{ service.title }}
          </h3>
          <p class="text-secondary-600 dark:text-secondary-300 mb-4 transition-colors duration-300">
            {{ service.description }}
          </p>

          <!-- Key Features -->
          <div class="mb-6">
            <h4 class="text-sm font-semibold text-secondary-700 dark:text-secondary-300 mb-3 uppercase tracking-wide">
              Key Features
            </h4>
            <div class="flex flex-wrap gap-2 justify-center">
              <span *ngFor="let feature of service.features.slice(0, 3)"
                    class="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 text-xs rounded-full transition-colors duration-300">
                {{ feature }}
              </span>
              <span *ngIf="service.features.length > 3"
                    class="px-3 py-1 bg-secondary-100 dark:bg-secondary-700 text-secondary-600 dark:text-secondary-300 text-xs rounded-full transition-colors duration-300">
                +{{ service.features.length - 3 }} more
              </span>
            </div>
          </div>

          <!-- Pricing -->
          <div class="mb-6" *ngIf="service.pricing">
            <div class="text-center p-4 bg-accent-50 dark:bg-accent-900/20 rounded-lg transition-colors duration-300">
              <div class="text-2xl font-bold text-accent-600 dark:text-accent-400 transition-colors duration-300">
                {{ service.pricing.starting }}
              </div>
              <div class="text-sm text-accent-700 dark:text-accent-300 transition-colors duration-300">
                {{ service.pricing.description }}
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="space-y-3">
            <a [routerLink]="service.route"
               class="block w-full btn-primary text-center">
              Learn More
            </a>
            <a routerLink="/contact"
               class="block w-full btn-outline text-center">
              Get Quote
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Why Choose Us Section -->
<section class="section-padding bg-secondary-50 dark:bg-secondary-800 transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Why Choose {{ companyName }}?
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        Experience the difference of working with certified professionals who understand your business needs
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.1s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Certified Experts</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Qualified CAs with extensive experience</p>
      </div>

      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.2s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Proven Track Record</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">500+ satisfied clients across industries</p>
      </div>

      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.3s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Timely Delivery</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Always on time, every time</p>
      </div>

      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.4s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">24/7 Support</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Round-the-clock assistance</p>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-gradient-to-r from-primary-600 to-primary-700 dark:from-primary-800 dark:to-primary-900 text-white transition-all duration-500">
  <div class="container-custom">
    <div class="text-center animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold mb-6">Ready to Get Started?</h2>
      <p class="text-xl text-primary-100 max-w-2xl mx-auto mb-8">
        Let our expert team help you achieve your financial goals with personalized solutions
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a routerLink="/contact" class="bg-white text-primary-600 hover:bg-primary-50 font-medium py-4 px-8 rounded-lg transition-colors duration-200 text-lg">
          Get Free Consultation
        </a>
        <a routerLink="/contact" class="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-4 px-8 rounded-lg transition-all duration-200 text-lg">
          Request Quote
        </a>
      </div>
    </div>
  </div>
</section>
