import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ConfigService } from '../../../core/services/config.service';
import { SEOService } from '../../../core/services/seo.service';

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  date: string;
  category: string;
  readTime: string;
  image: string;
  featured: boolean;
  tags: string[];
}

@Component({
  selector: 'app-blog',
  imports: [CommonModule, RouterModule],
  templateUrl: './blog.component.html',
  styleUrl: './blog.component.scss'
})
export class BlogComponent implements OnInit {
  companyName = '';
  selectedCategory = 'all';

  categories = [
    { id: 'all', name: 'All Articles', count: 45 },
    { id: 'tax', name: 'Tax Planning', count: 15 },
    { id: 'audit', name: 'Audit & Compliance', count: 12 },
    { id: 'business', name: 'Business Advisory', count: 10 },
    { id: 'gst', name: 'GST Updates', count: 8 }
  ];

  blogPosts: BlogPost[] = [
    {
      id: '1',
      title: 'New Tax Regulations 2024: Complete Guide for Businesses',
      excerpt: 'Comprehensive overview of the latest tax changes and their impact on your business operations and compliance requirements.',
      content: 'Full article content here...',
      author: 'CA Rajesh Kumar',
      date: '2024-03-15',
      category: 'tax',
      readTime: '8 min read',
      image: '/assets/images/blog/tax-2024.jpg',
      featured: true,
      tags: ['Tax Planning', 'Business', '2024 Updates']
    },
    {
      id: '2',
      title: 'GST Compliance Checklist: Avoid Common Mistakes',
      excerpt: 'Essential checklist to ensure your business stays compliant with GST regulations and avoids costly penalties.',
      content: 'Full article content here...',
      author: 'CA Priya Sharma',
      date: '2024-03-10',
      category: 'gst',
      readTime: '6 min read',
      image: '/assets/images/blog/gst-compliance.jpg',
      featured: false,
      tags: ['GST', 'Compliance', 'Checklist']
    },
    {
      id: '3',
      title: 'Digital Transformation in Accounting: What You Need to Know',
      excerpt: 'How modern accounting practices are evolving with technology and what it means for your business.',
      content: 'Full article content here...',
      author: 'CA Amit Patel',
      date: '2024-03-05',
      category: 'business',
      readTime: '10 min read',
      image: '/assets/images/blog/digital-accounting.jpg',
      featured: true,
      tags: ['Technology', 'Accounting', 'Digital Transformation']
    },
    {
      id: '4',
      title: 'Internal Audit Best Practices for Small Businesses',
      excerpt: 'Essential internal audit practices that can help small businesses improve their financial controls and compliance.',
      content: 'Full article content here...',
      author: 'CA Sneha Reddy',
      date: '2024-02-28',
      category: 'audit',
      readTime: '7 min read',
      image: '/assets/images/blog/internal-audit.jpg',
      featured: false,
      tags: ['Audit', 'Small Business', 'Best Practices']
    }
  ];

  constructor(
    private configService: ConfigService,
    private seoService: SEOService
  ) {}

  ngOnInit() {
    const config = this.configService.getConfig();
    this.companyName = config.branding.companyName;

    // Set SEO data for blog page
    this.seoService.updateSEO({
      title: 'Expert Financial Insights & Blog - Professional CA Articles',
      description: 'Read expert insights on tax planning, audit practices, business advisory, and GST compliance from our chartered accountants.',
      keywords: ['financial blog', 'tax insights', 'CA articles', 'business advice', 'accounting tips'],
      url: window.location.href
    });
  }

  filterByCategory(categoryId: string) {
    this.selectedCategory = categoryId;
  }

  get filteredPosts() {
    if (this.selectedCategory === 'all') {
      return this.blogPosts;
    }
    return this.blogPosts.filter(post => post.category === this.selectedCategory);
  }

  get featuredPosts() {
    return this.blogPosts.filter(post => post.featured);
  }

  getAuthorInitials(authorName: string): string {
    return authorName.split(' ').map(n => n[0]).join('');
  }
}
