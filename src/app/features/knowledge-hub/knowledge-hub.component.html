<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 dark:from-primary-800 dark:via-primary-900 dark:to-secondary-900 text-white py-20 lg:py-32 transition-all duration-500">
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0 bg-white bg-opacity-5"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="text-center animate-fade-in-up">
      <h1 class="text-4xl lg:text-6xl font-bold mb-6">Knowledge Hub</h1>
      <p class="text-xl lg:text-2xl text-primary-100 max-w-3xl mx-auto mb-8">
        Your comprehensive resource center for financial insights, tools, and expert guidance from {{ companyName }}
      </p>

      <!-- Quick Navigation -->
      <div class="flex flex-wrap justify-center gap-4 mt-8">
        <a *ngFor="let section of knowledgeSections"
           [routerLink]="section.route"
           class="px-6 py-3 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-all duration-300 hover:scale-105 backdrop-blur-sm border border-white/30">
          {{ section.icon }} {{ section.title }}
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Stats Section -->
<section class="section-padding bg-white dark:bg-secondary-900 transition-colors duration-500">
  <div class="container-custom">
    <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
      <div *ngFor="let stat of stats; let i = index"
           class="text-center animate-fade-in-up hover-lift"
           [style.animation-delay]="(0.1 + i * 0.1) + 's'">
        <div class="text-4xl mb-2">{{ stat.icon }}</div>
        <div class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-1 transition-colors duration-300">
          {{ stat.number }}
        </div>
        <div class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">
          {{ stat.label }}
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Knowledge Sections -->
<section class="section-padding bg-secondary-50 dark:bg-secondary-800 transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Explore Our Knowledge Center
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        Access expert insights, practical tools, and educational resources designed to help you make informed financial decisions
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div *ngFor="let section of knowledgeSections; let i = index"
           class="card group hover-lift animate-fade-in-up"
           [style.animation-delay]="(0.2 + i * 0.1) + 's'">

        <!-- Section Header -->
        <div class="flex items-center mb-6">
          <div class="w-16 h-16 bg-gradient-to-br {{ section.color }} rounded-lg flex items-center justify-center text-white text-2xl shadow-lg group-hover:scale-110 transition-transform duration-300">
            {{ section.icon }}
          </div>
          <div class="ml-4 flex-1">
            <h3 class="text-xl font-bold text-secondary-800 dark:text-white transition-colors duration-300">
              {{ section.title }}
            </h3>
            <div class="text-sm text-secondary-500 dark:text-secondary-400 transition-colors duration-300">
              {{ section.count }} items available
            </div>
          </div>
        </div>

        <!-- Section Description -->
        <p class="text-secondary-600 dark:text-secondary-300 mb-6 transition-colors duration-300">
          {{ section.description }}
        </p>

        <!-- Features -->
        <div class="mb-6">
          <div class="flex flex-wrap gap-2">
            <span *ngFor="let feature of section.features"
                  class="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 text-sm rounded-full transition-colors duration-300">
              {{ feature }}
            </span>
          </div>
        </div>

        <!-- Action Button -->
        <div class="mt-auto">
          <a [routerLink]="section.route"
             class="block w-full btn-primary text-center group-hover:shadow-lg transition-all duration-300">
            Explore {{ section.title }}
            <svg class="w-4 h-4 ml-2 inline-block group-hover:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-gradient-to-r from-primary-600 to-primary-700 dark:from-primary-800 dark:to-primary-900 text-white transition-all duration-500">
  <div class="container-custom">
    <div class="text-center animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold mb-6">Stay Updated with Latest Insights</h2>
      <p class="text-xl text-primary-100 max-w-2xl mx-auto mb-8">
        Subscribe to our newsletter and never miss important financial updates and expert insights
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
        <input type="email" placeholder="Enter your email"
               class="flex-1 px-4 py-3 rounded-lg text-secondary-900 focus:outline-none focus:ring-2 focus:ring-white">
        <button class="bg-white text-primary-600 hover:bg-primary-50 font-medium py-3 px-6 rounded-lg transition-colors duration-200">
          Subscribe
        </button>
      </div>
    </div>
  </div>
</section>