<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 dark:from-primary-800 dark:via-primary-900 dark:to-secondary-900 text-white py-20 lg:py-32 transition-all duration-500">
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0 bg-white bg-opacity-5"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="text-center animate-fade-in-up">
      <h1 class="text-4xl lg:text-6xl font-bold mb-6">About {{ companyName }}</h1>
      <p class="text-xl lg:text-2xl text-primary-100 max-w-3xl mx-auto mb-8">
        Your trusted financial partner since {{ establishedYear }}, providing expert chartered accountant services with integrity and excellence.
      </p>

      <!-- Section Navigation -->
      <div class="flex flex-wrap justify-center gap-4 mt-8">
        <a routerLink="/about"
           class="px-6 py-3 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-all duration-300 hover:scale-105 backdrop-blur-sm border border-white/30">
          Our Story
        </a>
        <a routerLink="/about/team"
           class="px-6 py-3 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-all duration-300 hover:scale-105 backdrop-blur-sm border border-white/30">
          Our Team
        </a>
        <a routerLink="/about/certifications"
           class="px-6 py-3 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-all duration-300 hover:scale-105 backdrop-blur-sm border border-white/30">
          Certifications
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Our Story Section -->
<section id="story" class="section-padding section-light transition-colors duration-500">
  <div class="container-custom">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
      <div class="animate-fade-in-left">
        <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-6 transition-colors duration-300">
          Our Story
        </h2>
        <div class="space-y-4 text-secondary-600 dark:text-secondary-300 transition-colors duration-300">
          <p class="text-lg leading-relaxed">
            Founded in {{ establishedYear }}, {{ companyName }} has been at the forefront of providing comprehensive financial and accounting services to businesses and individuals across India.
          </p>
          <p class="text-lg leading-relaxed">
            What started as a small practice has grown into a full-service chartered accountancy firm, serving over 500 clients across various industries. Our commitment to excellence, integrity, and personalized service has been the cornerstone of our success.
          </p>
          <p class="text-lg leading-relaxed">
            We believe in building long-term relationships with our clients, understanding their unique needs, and providing tailored solutions that drive growth and success.
          </p>
        </div>
      </div>

      <div class="relative animate-fade-in-right">
        <div class="glass p-8 hover-lift">
          <div class="grid grid-cols-2 gap-6">
            <div *ngFor="let achievement of achievements; let i = index"
                 class="text-center p-4 bg-white/10 dark:bg-white/5 rounded-lg hover-lift animate-scale-in"
                 [style.animation-delay]="(0.3 + i * 0.1) + 's'">
              <div class="text-3xl mb-2">{{ achievement.icon }}</div>
              <h3 class="font-semibold text-white mb-1">{{ achievement.title }}</h3>
              <p class="text-sm text-primary-200">{{ achievement.description }}</p>
            </div>
          </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute -top-4 -right-4 w-20 h-20 bg-accent-400 rounded-full opacity-20 animate-float"></div>
        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-primary-400 rounded-full opacity-30 animate-float" style="animation-delay: 1s;"></div>
      </div>
    </div>
  </div>
</section>

<!-- Our Team Section -->
<section id="team" class="section-padding bg-secondary-50 dark:bg-secondary-800 transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Meet Our Expert Team
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        Our experienced professionals are dedicated to providing you with the highest quality financial services
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div *ngFor="let member of teamMembers; let i = index"
           class="card text-center animate-fade-in-up hover-lift"
           [style.animation-delay]="(0.2 + i * 0.1) + 's'">
        <div class="relative mb-6">
          <div class="w-32 h-32 mx-auto bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white text-4xl font-bold shadow-lg">
            {{ member.name.split(' ')[0].charAt(0) }}{{ member.name.split(' ')[1] ? member.name.split(' ')[1].charAt(0) : '' }}
          </div>
          <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-accent-500 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>

        <h3 class="text-xl font-bold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">{{ member.name }}</h3>
        <p class="text-primary-600 dark:text-primary-400 font-medium mb-2 transition-colors duration-300">{{ member.position }}</p>
        <p class="text-secondary-600 dark:text-secondary-300 mb-3 transition-colors duration-300">{{ member.specialization }}</p>
        <p class="text-sm text-accent-600 dark:text-accent-400 font-medium mb-4 transition-colors duration-300">{{ member.experience }}</p>

        <div class="flex flex-wrap justify-center gap-2">
          <span *ngFor="let qualification of member.qualifications"
                class="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 text-xs rounded-full transition-colors duration-300">
            {{ qualification }}
          </span>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Certifications Section -->
<section id="certifications" class="section-padding bg-white dark:bg-secondary-900 transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Our Certifications & Credentials
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        Professional qualifications and certifications that ensure the highest standards of service
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div *ngFor="let cert of certifications; let i = index"
           class="card hover-lift animate-fade-in-up"
           [style.animation-delay]="(0.2 + i * 0.1) + 's'">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <div class="w-16 h-16 bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg flex items-center justify-center text-white text-2xl shadow-lg">
              {{ cert.icon }}
            </div>
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-lg font-semibold text-secondary-800 dark:text-white transition-colors duration-300">
                {{ cert.title }}
              </h3>
              <span class="text-sm font-medium text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/30 px-2 py-1 rounded transition-colors duration-300">
                {{ cert.year }}
              </span>
            </div>
            <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">
              {{ cert.description }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Values Section -->
<section class="section-padding bg-secondary-50 dark:bg-secondary-800 transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Our Core Values
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        The principles that guide everything we do
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.1s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Integrity</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Honest and transparent in all our dealings</p>
      </div>

      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.2s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Excellence</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Committed to delivering the highest quality services</p>
      </div>

      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.3s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Client Focus</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Your success is our primary objective</p>
      </div>

      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.4s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Innovation</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Embracing technology for better solutions</p>
      </div>
    </div>
  </div>
</section>