<!-- Enhanced Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 dark:from-primary-800 dark:via-primary-900 dark:to-secondary-900 text-white py-20 lg:py-32 transition-all duration-500 overflow-hidden">
  <!-- Advanced Background Effects -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-br from-primary-600/90 via-primary-700/95 to-primary-800/90 dark:from-primary-800/95 dark:via-primary-900/90 dark:to-secondary-900/95"></div>
    <div class="absolute inset-0 opacity-10 dark:opacity-5">
      <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%);"></div>
    </div>
    <!-- Floating Elements -->
    <div class="absolute top-20 right-20 w-3 h-3 bg-accent-400 rounded-full opacity-60 animate-float"></div>
    <div class="absolute bottom-32 left-16 w-2 h-2 bg-accent-300 rounded-full opacity-40 animate-float" style="animation-delay: 1s;"></div>
    <div class="absolute top-1/2 right-1/4 w-1 h-1 bg-white rounded-full opacity-50 animate-float" style="animation-delay: 2s;"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="text-center animate-fade-in-up">
      <!-- Professional Badge -->
      <div class="inline-flex items-center px-4 py-2 bg-white/10 dark:bg-white/5 backdrop-blur-sm rounded-full text-accent-300 text-sm font-medium mb-6 border border-white/20 animate-fade-in-up">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        About Our Firm
      </div>

      <h1 class="text-5xl lg:text-7xl font-bold mb-8 leading-tight animate-fade-in-up" style="animation-delay: 0.1s;">
        <span class="block text-white mb-2">About</span>
        <span class="block text-gradient bg-gradient-to-r from-accent-400 via-accent-300 to-accent-200 bg-clip-text text-transparent animate-glow">
          {{ companyName }}
        </span>
      </h1>

      <p class="text-xl lg:text-2xl text-primary-100 dark:text-primary-200 max-w-4xl mx-auto mb-12 leading-relaxed animate-fade-in-up" style="animation-delay: 0.2s;">
        Your <span class="font-semibold text-accent-300">trusted financial partner</span> since {{ establishedYear }}, providing expert chartered accountant services with integrity and excellence.
      </p>

      <!-- Enhanced Section Navigation -->
      <div class="flex flex-wrap justify-center gap-6 mt-12 animate-fade-in-up" style="animation-delay: 0.3s;">
        <a routerLink="/about"
           class="group relative inline-flex items-center px-8 py-4 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-white/20 hover:border-white/30 shadow-lg hover:shadow-xl">
          <svg class="w-5 h-5 mr-3 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clip-rule="evenodd"></path>
          </svg>
          <span class="font-semibold">Our Story</span>
        </a>
        <a routerLink="/about/team"
           class="group relative inline-flex items-center px-8 py-4 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-white/20 hover:border-white/30 shadow-lg hover:shadow-xl">
          <svg class="w-5 h-5 mr-3 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
          </svg>
          <span class="font-semibold">Our Team</span>
        </a>
        <a routerLink="/about/certifications"
           class="group relative inline-flex items-center px-8 py-4 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-white/20 hover:border-white/30 shadow-lg hover:shadow-xl">
          <svg class="w-5 h-5 mr-3 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <span class="font-semibold">Certifications</span>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Our Story Section -->
<section id="story" class="section-padding section-light transition-colors duration-500">
  <div class="container-custom">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
      <div class="animate-fade-in-left">
        <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-6 transition-colors duration-300">
          Our Story
        </h2>
        <div class="space-y-4 text-secondary-600 dark:text-secondary-300 transition-colors duration-300">
          <p class="text-lg leading-relaxed">
            Founded in {{ establishedYear }}, {{ companyName }} has been at the forefront of providing comprehensive financial and accounting services to businesses and individuals across India.
          </p>
          <p class="text-lg leading-relaxed">
            What started as a small practice has grown into a full-service chartered accountancy firm, serving over 500 clients across various industries. Our commitment to excellence, integrity, and personalized service has been the cornerstone of our success.
          </p>
          <p class="text-lg leading-relaxed">
            We believe in building long-term relationships with our clients, understanding their unique needs, and providing tailored solutions that drive growth and success.
          </p>
        </div>
      </div>

      <div class="relative animate-fade-in-right">
        <div class="glass p-8 hover-lift">
          <div class="grid grid-cols-2 gap-6">
            <div *ngFor="let achievement of achievements; let i = index"
                 class="text-center p-4 bg-white/10 dark:bg-white/5 rounded-lg hover-lift animate-scale-in"
                 [style.animation-delay]="(0.3 + i * 0.1) + 's'">
              <div class="text-3xl mb-2">{{ achievement.icon }}</div>
              <h3 class="font-semibold text-white mb-1">{{ achievement.title }}</h3>
              <p class="text-sm text-primary-200">{{ achievement.description }}</p>
            </div>
          </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute -top-4 -right-4 w-20 h-20 bg-accent-400 rounded-full opacity-20 animate-float"></div>
        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-primary-400 rounded-full opacity-30 animate-float" style="animation-delay: 1s;"></div>
      </div>
    </div>
  </div>
</section>

<!-- Our Team Section -->
<section id="team" class="section-padding bg-secondary-50 dark:bg-secondary-800 transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Meet Our Expert Team
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        Our experienced professionals are dedicated to providing you with the highest quality financial services
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div *ngFor="let member of teamMembers; let i = index"
           class="group relative bg-white/50 dark:bg-secondary-700/50 backdrop-blur-sm rounded-3xl p-8 border border-secondary-200/50 dark:border-secondary-600/50 hover:shadow-2xl hover:shadow-primary-500/10 dark:hover:shadow-primary-400/10 transition-all duration-500 hover:-translate-y-2 animate-fade-in-up"
           [style.animation-delay]="(0.2 + i * 0.1) + 's'">

        <!-- Background Gradient -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-50/50 to-accent-50/50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        <div class="relative z-10 text-center">
          <!-- Enhanced Avatar -->
          <div class="relative mb-8">
            <div class="w-36 h-36 mx-auto bg-gradient-to-br from-primary-500 via-primary-600 to-accent-500 rounded-full flex items-center justify-center text-white text-5xl font-bold shadow-2xl group-hover:scale-110 transition-transform duration-500 border-4 border-white dark:border-secondary-600">
              {{ member.name.split(' ')[0].charAt(0) }}{{ member.name.split(' ')[1] ? member.name.split(' ')[1].charAt(0) : '' }}
            </div>

            <!-- Verification Badge -->
            <div class="absolute -bottom-2 -right-2 w-12 h-12 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 border-3 border-white dark:border-secondary-600">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>

            <!-- Floating Elements -->
            <div class="absolute -top-2 -left-2 w-3 h-3 bg-accent-400 rounded-full opacity-60 animate-float"></div>
            <div class="absolute -bottom-4 -left-4 w-2 h-2 bg-primary-400 rounded-full opacity-40 animate-float" style="animation-delay: 1s;"></div>
          </div>

          <!-- Member Info -->
          <div class="space-y-4">
            <h3 class="text-2xl font-bold text-secondary-800 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">{{ member.name }}</h3>

            <div class="inline-flex items-center px-4 py-2 bg-primary-100 dark:bg-primary-900/30 rounded-full text-primary-600 dark:text-primary-400 font-semibold text-sm transition-colors duration-300">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
              </svg>
              {{ member.position }}
            </div>

            <p class="text-secondary-600 dark:text-secondary-300 font-medium transition-colors duration-300">{{ member.specialization }}</p>

            <!-- Experience Badge -->
            <div class="flex items-center justify-center">
              <div class="inline-flex items-center px-4 py-2 bg-accent-100 dark:bg-accent-900/30 rounded-full text-accent-600 dark:text-accent-400 font-medium text-sm transition-colors duration-300">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                </svg>
                {{ member.experience }}
              </div>
            </div>

            <!-- Qualifications -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-secondary-700 dark:text-secondary-300 uppercase tracking-wide">Qualifications</h4>
              <div class="flex flex-wrap justify-center gap-2">
                <span *ngFor="let qualification of member.qualifications; let j = index"
                      class="px-3 py-2 bg-gradient-to-r from-secondary-100 to-secondary-200 dark:from-secondary-600 dark:to-secondary-500 text-secondary-700 dark:text-secondary-200 text-xs font-bold rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-md"
                      [style.animation-delay]="(0.5 + j * 0.1) + 's'">
                  {{ qualification }}
                </span>
              </div>
            </div>

            <!-- Contact Button -->
            <div class="pt-4">
              <button class="group/btn inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-600 to-accent-600 hover:from-primary-700 hover:to-accent-700 text-white font-medium rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/25 hover:-translate-y-1">
                <svg class="w-4 h-4 mr-2 group-hover/btn:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                </svg>
                <span>Contact</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Certifications Section -->
<section id="certifications" class="section-padding bg-white dark:bg-secondary-900 transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Our Certifications & Credentials
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        Professional qualifications and certifications that ensure the highest standards of service
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div *ngFor="let cert of certifications; let i = index"
           class="card hover-lift animate-fade-in-up"
           [style.animation-delay]="(0.2 + i * 0.1) + 's'">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <div class="w-16 h-16 bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg flex items-center justify-center text-white text-2xl shadow-lg">
              {{ cert.icon }}
            </div>
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-lg font-semibold text-secondary-800 dark:text-white transition-colors duration-300">
                {{ cert.title }}
              </h3>
              <span class="text-sm font-medium text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/30 px-2 py-1 rounded transition-colors duration-300">
                {{ cert.year }}
              </span>
            </div>
            <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">
              {{ cert.description }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Values Section -->
<section class="section-padding bg-secondary-50 dark:bg-secondary-800 transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Our Core Values
      </h2>
      <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto transition-colors duration-300">
        The principles that guide everything we do
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.1s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Integrity</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Honest and transparent in all our dealings</p>
      </div>

      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.2s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Excellence</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Committed to delivering the highest quality services</p>
      </div>

      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.3s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Client Focus</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Your success is our primary objective</p>
      </div>

      <div class="text-center animate-fade-in-up hover-lift" style="animation-delay: 0.4s;">
        <div class="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center transition-colors duration-300">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">Innovation</h3>
        <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">Embracing technology for better solutions</p>
      </div>
    </div>
  </div>
</section>