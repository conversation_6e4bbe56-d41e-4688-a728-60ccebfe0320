import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfigService } from '../../core/services/config.service';
import { SEOService } from '../../core/services/seo.service';

@Component({
  selector: 'app-contact',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss'
})
export class ContactComponent implements OnInit {
  contactForm: FormGroup;
  isSubmitting = false;
  submitSuccess = false;
  submitError = false;

  companyName = '';
  phone = '';
  email = '';
  address = '';

  contactMethods = [
    {
      icon: '📞',
      title: 'Phone',
      value: '+91-9876543210',
      description: 'Mon-Fri 9AM-6PM',
      action: 'tel:+919876543210'
    },
    {
      icon: '✉️',
      title: 'Email',
      value: '<EMAIL>',
      description: 'We reply within 24 hours',
      action: 'mailto:<EMAIL>'
    },
    {
      icon: '📍',
      title: 'Office',
      value: 'Mumbai, Maharashtra',
      description: 'Visit us for consultation',
      action: '#'
    },
    {
      icon: '💬',
      title: 'WhatsApp',
      value: '+91-9876543210',
      description: 'Quick support',
      action: 'https://wa.me/919876543210'
    }
  ];

  constructor(
    private fb: FormBuilder,
    private configService: ConfigService,
    private seoService: SEOService
  ) {
    this.contactForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      company: [''],
      service: ['', Validators.required],
      message: ['', [Validators.required, Validators.minLength(10)]]
    });
  }

  ngOnInit() {
    const config = this.configService.getConfig();
    this.companyName = config.branding.companyName;
    this.phone = config.contact.phone;
    this.email = config.contact.email;
    this.address = this.configService.getAddress();

    // Set SEO data for contact page
    this.seoService.updateSEO({
      title: 'Contact Us - Get Professional CA Services',
      description: 'Contact our expert chartered accountants for professional financial services. Call, email, or visit our office in Mumbai for consultation.',
      keywords: ['contact ca', 'chartered accountant contact', 'financial consultation', 'tax advice'],
      url: window.location.href
    });
  }

  onSubmit() {
    if (this.contactForm.valid) {
      this.isSubmitting = true;
      this.submitError = false;

      // Simulate form submission
      setTimeout(() => {
        this.isSubmitting = false;
        this.submitSuccess = true;
        this.contactForm.reset();

        // Hide success message after 5 seconds
        setTimeout(() => {
          this.submitSuccess = false;
        }, 5000);
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.contactForm.controls).forEach(key => {
        this.contactForm.get(key)?.markAsTouched();
      });
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.contactForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['pattern']) return 'Please enter a valid phone number';
      if (field.errors['minlength']) return `${fieldName} is too short`;
    }
    return '';
  }
}
