<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 dark:from-primary-800 dark:via-primary-900 dark:to-secondary-900 text-white py-20 lg:py-32 transition-all duration-500">
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0 bg-white bg-opacity-5"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="text-center animate-fade-in-up">
      <h1 class="text-4xl lg:text-6xl font-bold mb-6">Contact Us</h1>
      <p class="text-xl lg:text-2xl text-primary-100 max-w-3xl mx-auto">
        Ready to take control of your finances? Get in touch with our expert team for professional consultation.
      </p>
    </div>
  </div>
</section>

<!-- Contact Methods Section -->
<section class="section-padding section-light transition-colors duration-500">
  <div class="container-custom">
    <div class="text-center mb-16 animate-fade-in-up">
      <h2 class="text-3xl lg:text-4xl font-bold mb-4 transition-colors duration-300 section-heading">
        Get In Touch
      </h2>
      <p class="text-xl max-w-3xl mx-auto transition-colors duration-300 section-text">
        Choose your preferred way to reach us. We're here to help with all your financial needs.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
      <div *ngFor="let method of contactMethods; let i = index"
           class="card text-center hover-lift animate-fade-in-up group relative overflow-hidden"
           [style.animation-delay]="(0.1 + i * 0.1) + 's'">
        <!-- Floating background elements -->
        <div class="absolute -top-2 -right-2 w-8 h-8 bg-primary-400/20 rounded-full animate-float" [style.animation-delay]="(i * 0.5) + 's'"></div>
        <div class="absolute -bottom-2 -left-2 w-6 h-6 bg-accent-400/20 rounded-full animate-float" [style.animation-delay]="(i * 0.5 + 1) + 's'"></div>

        <div class="text-4xl mb-4 animate-bounce-slow group-hover:animate-wiggle transition-all duration-300">{{ method.icon }}</div>
        <h3 class="text-lg font-semibold mb-2 transition-colors duration-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 section-heading">{{ method.title }}</h3>
        <a [href]="method.action"
           class="text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300 transition-all duration-300 block mb-2 hover:scale-105 transform">
          {{ method.value }}
        </a>
        <p class="text-sm transition-colors duration-300 section-text">{{ method.description }}</p>

        <!-- Hover effect overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-accent-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
      </div>
    </div>
  </div>
</section>

<!-- Contact Form Section -->
<section class="section-padding section-accent transition-colors duration-500">
  <div class="container-custom">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
      <!-- Contact Form -->
      <div class="animate-fade-in-left">
        <h2 class="text-3xl font-bold mb-6 transition-colors duration-300 contact-form-heading">
          Send Us a Message
        </h2>
        <p class="mb-8 transition-colors duration-300 contact-form-text">
          Fill out the form below and we'll get back to you within 24 hours.
        </p>

        <!-- Success Message -->
        <div *ngIf="submitSuccess"
             class="mb-6 p-4 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded-lg animate-fade-in">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-green-700 dark:text-green-300 font-medium">Thank you! Your message has been sent successfully.</span>
          </div>
        </div>

        <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Name Field -->
            <div class="animate-fade-in-up" style="animation-delay: 0.1s;">
              <label for="name" class="form-label">
                Full Name *
              </label>
              <input
                type="text"
                id="name"
                formControlName="name"
                class="form-input"
                placeholder="Enter your full name"
              >
              <p *ngIf="getFieldError('name')" class="mt-1 text-sm text-red-600 dark:text-red-400 animate-fade-in">
                {{ getFieldError('name') }}
              </p>
            </div>

            <!-- Email Field -->
            <div class="animate-fade-in-up" style="animation-delay: 0.2s;">
              <label for="email" class="form-label">
                Email Address *
              </label>
              <input
                type="email"
                id="email"
                formControlName="email"
                class="form-input"
                placeholder="Enter your email"
              >
              <p *ngIf="getFieldError('email')" class="mt-1 text-sm text-red-600 dark:text-red-400 animate-fade-in">
                {{ getFieldError('email') }}
              </p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Phone Field -->
            <div class="animate-fade-in-up" style="animation-delay: 0.3s;">
              <label for="phone" class="form-label">
                Phone Number *
              </label>
              <input
                type="tel"
                id="phone"
                formControlName="phone"
                class="form-input"
                placeholder="Enter your phone number"
              >
              <p *ngIf="getFieldError('phone')" class="mt-1 text-sm text-red-600 dark:text-red-400 animate-fade-in">
                {{ getFieldError('phone') }}
              </p>
            </div>

            <!-- Company Field -->
            <div class="animate-fade-in-up" style="animation-delay: 0.4s;">
              <label for="company" class="form-label">
                Company Name
              </label>
              <input
                type="text"
                id="company"
                formControlName="company"
                class="form-input"
                placeholder="Enter your company name"
              >
            </div>
          </div>

          <!-- Service Field -->
          <div class="animate-fade-in-up" style="animation-delay: 0.5s;">
            <label for="service" class="form-label">
              Service Required *
            </label>
            <select
              id="service"
              formControlName="service"
              class="form-input"
            >
              <option value="">Select a service</option>
              <option value="tax-planning">Tax Planning & Preparation</option>
              <option value="audit">Audit & Assurance</option>
              <option value="advisory">Business Advisory</option>
              <option value="gst">GST Services</option>
              <option value="registration">Company Registration</option>
              <option value="financial-planning">Financial Planning</option>
              <option value="other">Other</option>
            </select>
            <p *ngIf="getFieldError('service')" class="mt-1 text-sm text-red-600 dark:text-red-400 animate-fade-in">
              {{ getFieldError('service') }}
            </p>
          </div>

          <!-- Message Field -->
          <div class="animate-fade-in-up" style="animation-delay: 0.6s;">
            <label for="message" class="form-label">
              Message *
            </label>
            <textarea
              id="message"
              formControlName="message"
              rows="5"
              class="form-input resize-none"
              placeholder="Tell us about your requirements..."
            ></textarea>
            <p *ngIf="getFieldError('message')" class="mt-1 text-sm text-red-600 dark:text-red-400 animate-fade-in">
              {{ getFieldError('message') }}
            </p>
          </div>

          <!-- Submit Button -->
          <div class="animate-fade-in-up" style="animation-delay: 0.7s;">
            <button
              type="submit"
              [disabled]="isSubmitting"
              class="w-full btn-primary text-lg py-4 hover-lift hover-glow disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center animate-pulse-slow"
            >
              <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="relative">
                {{ isSubmitting ? 'Sending...' : 'Send Message' }}
                <svg *ngIf="!isSubmitting" class="w-5 h-5 ml-2 inline-block animate-bounce-slow" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path>
                </svg>
              </span>
            </button>
          </div>
        </form>
      </div>

      <!-- Contact Information -->
      <div class="animate-fade-in-right">
        <div class="card p-8 h-fit relative">
          <h3 class="text-2xl font-bold mb-6 contact-form-heading">Contact Information</h3>

          <div class="space-y-6">
            <div class="flex items-start">
              <div class="w-12 h-12 bg-primary-500 dark:bg-primary-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold mb-1 section-heading">Office Address</h4>
                <p class="section-text">{{ address }}</p>
              </div>
            </div>

            <div class="flex items-start">
              <div class="w-12 h-12 bg-primary-500 dark:bg-primary-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold mb-1 section-heading">Phone Number</h4>
                <a [href]="'tel:' + phone" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200 font-medium">{{ phone }}</a>
              </div>
            </div>

            <div class="flex items-start">
              <div class="w-12 h-12 bg-primary-500 dark:bg-primary-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold mb-1 section-heading">Email Address</h4>
                <a [href]="'mailto:' + email" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200 font-medium">{{ email }}</a>
              </div>
            </div>

            <div class="flex items-start">
              <div class="w-12 h-12 bg-primary-500 dark:bg-primary-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold mb-1 section-heading">Business Hours</h4>
                <div class="section-text text-sm">
                  <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                  <p>Saturday: 9:00 AM - 2:00 PM</p>
                  <p>Sunday: Closed</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Floating Elements -->
          <div class="absolute -top-4 -right-4 w-20 h-20 bg-accent-400/20 rounded-full opacity-30 animate-float"></div>
          <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-primary-400/20 rounded-full opacity-40 animate-float" style="animation-delay: 1s;"></div>
        </div>
      </div>
    </div>
  </div>
</section>