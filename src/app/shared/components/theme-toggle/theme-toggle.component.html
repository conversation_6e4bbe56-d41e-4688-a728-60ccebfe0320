<button
  (click)="toggleTheme()"
  class="theme-toggle relative inline-flex items-center justify-center w-14 h-7 bg-gradient-to-r from-secondary-200 to-secondary-300 dark:from-secondary-700 dark:to-secondary-600 rounded-full transition-all duration-500 hover:from-secondary-300 hover:to-secondary-400 dark:hover:from-secondary-600 dark:hover:to-secondary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-secondary-800 shadow-lg hover:shadow-xl transform hover:scale-105 border border-secondary-300 dark:border-secondary-600"
  [attr.aria-label]="isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'"
>
  <!-- Background Icons -->
  <div class="absolute inset-0 flex items-center justify-between px-1.5">
    <!-- Sun Icon Background -->
    <svg
      class="theme-icon sun-icon w-3 h-3 text-yellow-400 opacity-60 transition-all duration-500"
      [class.opacity-100]="!isDarkMode"
      [class.scale-110]="!isDarkMode"
      fill="currentColor"
      viewBox="0 0 20 20"
    >
      <path
        fill-rule="evenodd"
        d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
        clip-rule="evenodd"
      />
    </svg>

    <!-- Moon Icon Background -->
    <svg
      class="theme-icon moon-icon w-3 h-3 text-blue-400 opacity-60 transition-all duration-500"
      [class.opacity-100]="isDarkMode"
      [class.scale-110]="isDarkMode"
      fill="currentColor"
      viewBox="0 0 20 20"
    >
      <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
    </svg>
  </div>

  <!-- Toggle Circle -->
  <span
    class="toggle-circle absolute left-0.5 w-6 h-6 bg-gradient-to-br from-white to-secondary-50 dark:from-secondary-100 dark:to-secondary-200 rounded-full shadow-lg transform transition-all duration-500 ease-out border border-secondary-200 dark:border-secondary-300 flex items-center justify-center"
    [class.translate-x-7]="isDarkMode"
    [class.rotate-180]="isDarkMode"
    [class.light-mode]="!isDarkMode"
    [class.dark-mode]="isDarkMode"
  >
    <!-- Active Icon -->
    <svg
      *ngIf="!isDarkMode"
      class="w-4 h-4 text-yellow-500 transition-all duration-300 animate-pulse"
      fill="currentColor"
      viewBox="0 0 20 20"
    >
      <path
        fill-rule="evenodd"
        d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
        clip-rule="evenodd"
      />
    </svg>

    <svg
      *ngIf="isDarkMode"
      class="w-4 h-4 text-blue-500 transition-all duration-300"
      fill="currentColor"
      viewBox="0 0 20 20"
    >
      <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
    </svg>
  </span>
</button>
