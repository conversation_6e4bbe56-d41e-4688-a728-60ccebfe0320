<!-- Newsletter Section -->
<section class="bg-primary-600 dark:bg-primary-800 text-white py-16 transition-colors duration-500">
  <div class="container-custom">
    <div class="max-w-4xl mx-auto text-center animate-fade-in-up">
      <h2 class="text-3xl font-bold mb-4">Stay Updated with Tax & Business Insights</h2>
      <p class="text-primary-100 dark:text-primary-200 mb-8 transition-colors duration-300">Get the latest updates on tax laws, business regulations, and financial planning tips delivered to your inbox.</p>

      <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
        <input
          type="email"
          placeholder="Enter your email address"
          class="flex-1 px-4 py-3 rounded-lg text-secondary-800 dark:text-secondary-200 bg-white dark:bg-secondary-800 border border-secondary-300 dark:border-secondary-600 focus:outline-none focus:ring-2 focus:ring-accent-400 transition-all duration-300"
        >
        <button
          type="submit"
          class="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 hover-lift hover-glow"
        >
          Subscribe
        </button>
      </form>

      <p class="text-primary-200 text-sm mt-4">
        We respect your privacy. Unsubscribe at any time.
      </p>
    </div>
  </div>
</section>

<!-- Main Footer -->
<footer class="bg-secondary-800 dark:bg-secondary-900 text-white transition-colors duration-500">
  <div class="container-custom py-16">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

      <!-- Company Info -->
      <div class="lg:col-span-1">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mr-3">
            <span class="text-white font-bold text-xl">CA</span>
          </div>
          <div>
            <h3 class="text-xl font-bold">{{ companyName }}</h3>
            <p class="text-secondary-300 text-sm">Your Trusted Financial Partner</p>
          </div>
        </div>

        <p class="text-secondary-300 mb-6 leading-relaxed">
          Professional chartered accountant services with over a decade of experience in tax planning,
          audit, and business advisory. We help individuals and businesses achieve their financial goals.
        </p>

        <div class="flex space-x-4">
          <a href="#" class="w-10 h-10 bg-secondary-700 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.761 0 5-2.239 5-5v-14c0-2.761-2.239-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
            </svg>
          </a>
          <a href="#" class="w-10 h-10 bg-secondary-700 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </a>
          <a href="#" class="w-10 h-10 bg-secondary-700 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
            </svg>
          </a>
          <a href="#" class="w-10 h-10 bg-secondary-700 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
            </svg>
          </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div>
        <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
        <ul class="space-y-3">
          <li *ngFor="let link of quickLinks">
            <a
              [routerLink]="link.route"
              class="text-secondary-300 hover:text-white transition-colors duration-200"
            >
              {{ link.label }}
            </a>
          </li>
        </ul>
      </div>

      <!-- Services -->
      <div>
        <h4 class="text-lg font-semibold mb-6">Our Services</h4>
        <ul class="space-y-3">
          <li *ngFor="let service of services">
            <a
              [routerLink]="service.route"
              class="text-secondary-300 hover:text-white transition-colors duration-200"
            >
              {{ service.label }}
            </a>
          </li>
        </ul>
      </div>

      <!-- Contact Info -->
      <div>
        <h4 class="text-lg font-semibold mb-6">Contact Information</h4>
        <div class="space-y-4">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-accent-400 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
            </svg>
            <p class="text-secondary-300">{{ address }}</p>
          </div>

          <div class="flex items-center">
            <svg class="w-5 h-5 text-accent-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
            </svg>
            <a [href]="'tel:' + phone" class="text-secondary-300 hover:text-white transition-colors duration-200">
              {{ phone }}
            </a>
          </div>

          <div class="flex items-center">
            <svg class="w-5 h-5 text-accent-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
            </svg>
            <a [href]="'mailto:' + email" class="text-secondary-300 hover:text-white transition-colors duration-200">
              {{ email }}
            </a>
          </div>

          <div class="mt-6">
            <h5 class="text-white font-medium mb-3">Business Hours</h5>
            <div class="text-secondary-300 text-sm space-y-1">
              <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
              <p>Saturday: 9:00 AM - 2:00 PM</p>
              <p>Sunday: Closed</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Bar -->
  <div class="border-t border-secondary-700">
    <div class="container-custom py-6">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <p class="text-secondary-300 text-sm mb-4 md:mb-0">
          © {{ currentYear }} {{ companyName }}. All rights reserved.
        </p>
        <div class="flex items-center space-x-6 text-sm">
          <a routerLink="/privacy" class="text-secondary-300 hover:text-white transition-colors duration-200">
            Privacy Policy
          </a>
          <a routerLink="/terms" class="text-secondary-300 hover:text-white transition-colors duration-200">
            Terms of Service
          </a>
          <a routerLink="/sitemap" class="text-secondary-300 hover:text-white transition-colors duration-200">
            Sitemap
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>
