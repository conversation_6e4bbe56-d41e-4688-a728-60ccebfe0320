// Header component specific styles

// Logo hover effects
.logo-container {
  &:hover {
    .logo-icon {
      transform: scale(1.05) rotate(5deg);
    }

    .company-name {
      color: var(--primary-600);
    }
  }
}

// Navigation enhancements
.nav-item {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-600), var(--primary-500));
    transition: width 0.3s ease;
  }

  &:hover::after,
  &.active::after {
    width: 100%;
  }
}

// Dropdown menu enhancements
.dropdown-menu {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);

  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgba(255, 255, 255, 0.95);
  }
}

.dark .dropdown-menu {
  background: rgba(31, 41, 55, 0.95);

  &::before {
    border-bottom-color: rgba(31, 41, 55, 0.95);
  }
}

// CTA button enhancements
.cta-buttons {
  .btn-outline {
    border: 2px solid var(--primary-600);
    font-weight: 600;

    &:hover {
      background: var(--primary-600);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
    }
  }

  .btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    font-weight: 600;

    &:hover {
      background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(37, 99, 235, 0.4);
    }
  }
}

// Mobile menu improvements
.mobile-menu {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);

  .mobile-nav-item {
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:hover {
      background: rgba(37, 99, 235, 0.05);
      padding-left: 8px;
      transition: all 0.3s ease;
    }
  }
}

.dark .mobile-menu {
  background: rgba(31, 41, 55, 0.98);

  .mobile-nav-item {
    border-bottom-color: rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(37, 99, 235, 0.1);
    }
  }
}

// Header scroll effect
.header-scrolled {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);

  .logo-icon {
    transform: scale(0.9);
  }

  .company-name {
    font-size: 1.25rem;
  }
}

.dark .header-scrolled {
  background: rgba(31, 41, 55, 0.95);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

// Responsive adjustments
@media (max-width: 1024px) {
  .nav-item {
    font-size: 0.95rem;
  }

  .cta-buttons {
    .btn-outline,
    .btn-primary {
      padding: 8px 16px;
      font-size: 0.9rem;
    }
  }
}

// Animation for mobile menu toggle
.mobile-menu-enter {
  animation: slideDown 0.3s ease-out;
}

.mobile-menu-leave {
  animation: slideUp 0.3s ease-in;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

// Focus states for accessibility
.nav-item:focus,
.cta-buttons a:focus {
  outline: 2px solid var(--primary-600);
  outline-offset: 2px;
  border-radius: 4px;
}

// Print styles
@media print {
  .header {
    box-shadow: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .cta-buttons,
  .mobile-menu-button {
    display: none;
  }
}