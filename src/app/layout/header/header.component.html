<!-- Top Bar -->
<div class="bg-gradient-to-r from-secondary-800 to-secondary-900 dark:from-secondary-900 dark:to-black text-white py-3 hidden lg:block transition-all duration-300">
  <div class="container-custom">
    <div class="flex justify-between items-center text-sm">
      <div class="flex items-center space-x-8">
        <a [href]="'tel:' + phone" class="flex items-center hover:text-accent-400 transition-all duration-300 hover:scale-105 group">
          <div class="w-8 h-8 bg-accent-500/20 rounded-full flex items-center justify-center mr-3 group-hover:bg-accent-500/30 transition-all duration-300">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
            </svg>
          </div>
          <span class="font-medium">{{ phone }}</span>
        </a>
        <a [href]="'mailto:' + email" class="flex items-center hover:text-accent-400 transition-all duration-300 hover:scale-105 group">
          <div class="w-8 h-8 bg-accent-500/20 rounded-full flex items-center justify-center mr-3 group-hover:bg-accent-500/30 transition-all duration-300">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
            </svg>
          </div>
          <span class="font-medium">{{ email }}</span>
        </a>
      </div>
      <div class="flex items-center space-x-6">
        <div class="flex items-center text-accent-400 font-medium">
          <div class="w-8 h-8 bg-accent-500/20 rounded-full flex items-center justify-center mr-3">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          Mumbai, Maharashtra
        </div>
        <div class="flex space-x-4">
          <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-accent-500 hover:scale-110 transition-all duration-300 group">
            <svg class="w-4 h-4 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.761 0 5-2.239 5-5v-14c0-2.761-2.239-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
            </svg>
          </a>
          <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-accent-500 hover:scale-110 transition-all duration-300 group">
            <svg class="w-4 h-4 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Main Header -->
<header class="bg-white dark:bg-secondary-900 shadow-lg dark:shadow-2xl sticky top-0 z-50 transition-all duration-300 border-b border-transparent dark:border-secondary-700">
  <div class="container-custom">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <div class="flex items-center">
        <a routerLink="/" class="flex items-center group">
          <div class="w-12 h-12 bg-gradient-to-br from-primary-600 to-primary-700 rounded-xl flex items-center justify-center mr-3 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
            <span class="text-white font-bold text-xl">CA</span>
          </div>
          <div>
            <h1 class="text-xl font-bold text-secondary-800 dark:text-white transition-colors duration-300 group-hover:text-primary-600 dark:group-hover:text-primary-400">{{ companyName }}</h1>
            <p class="text-xs text-secondary-600 dark:text-secondary-300 transition-colors duration-300 font-medium">Your Trusted Financial Partner</p>
          </div>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <nav class="hidden lg:flex items-center space-x-8">
        <div *ngFor="let item of navigationItems" class="relative group">
          <a
            [routerLink]="item.route"
            class="text-secondary-700 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 font-semibold text-base transition-all duration-300 flex items-center py-2 px-1 relative group-hover:scale-105"
            [class.text-primary-600]="item.hasDropdown"
            routerLinkActive="text-primary-600 dark:text-primary-400"
          >
            {{ item.label }}
            <svg *ngIf="item.hasDropdown" class="w-4 h-4 ml-1 group-hover:rotate-180 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
            <!-- Active indicator -->
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"></span>
          </a>

          <!-- Dropdown Menu -->
          <div *ngIf="item.hasDropdown" class="absolute top-full left-0 mt-3 w-64 bg-white dark:bg-secondary-800 rounded-xl shadow-2xl border border-secondary-200 dark:border-secondary-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50">
            <div class="py-3">
              <a
                *ngFor="let dropdownItem of item.dropdownItems"
                [routerLink]="dropdownItem.route"
                class="block px-6 py-3 text-secondary-700 dark:text-secondary-300 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-200 font-medium hover:translate-x-1"
              >
                {{ dropdownItem.label }}
              </a>
            </div>
          </div>
        </div>
      </nav>

      <!-- CTA Buttons -->
      <div class="hidden lg:flex items-center space-x-6">
        <div class="mr-2">
          <app-theme-toggle></app-theme-toggle>
        </div>
        <a routerLink="/contact" class="btn-outline hover:scale-105 transition-transform duration-300 text-sm px-4 py-2">
          Get Quote
        </a>
        <a routerLink="/contact" class="btn-primary hover:scale-105 transition-transform duration-300 shadow-lg hover:shadow-xl text-sm px-4 py-2">
          Book Consultation
        </a>
      </div>

      <!-- Mobile Menu Button -->
      <button
        (click)="toggleMenu()"
        class="lg:hidden p-2 rounded-md text-secondary-600 hover:text-primary-600 hover:bg-secondary-100 transition-colors duration-200"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path *ngIf="!isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          <path *ngIf="isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div
    class="lg:hidden bg-white dark:bg-secondary-900 border-t border-secondary-200 dark:border-secondary-700 transition-all duration-300 ease-in-out"
    [class.max-h-0]="!isMenuOpen"
    [class.max-h-screen]="isMenuOpen"
    [class.overflow-hidden]="!isMenuOpen"
  >
    <div class="container-custom py-4">
      <nav class="space-y-4">
        <div *ngFor="let item of navigationItems">
          <a
            [routerLink]="item.route"
            (click)="closeMenu()"
            class="block py-2 text-secondary-700 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-200"
          >
            {{ item.label }}
          </a>
          <div *ngIf="item.hasDropdown" class="ml-4 space-y-2 mt-2">
            <a
              *ngFor="let dropdownItem of item.dropdownItems"
              [routerLink]="dropdownItem.route"
              (click)="closeMenu()"
              class="block py-1 text-secondary-600 dark:text-secondary-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
            >
              {{ dropdownItem.label }}
            </a>
          </div>
        </div>
      </nav>

      <div class="mt-6 space-y-3">
        <div class="flex justify-center mb-4">
          <app-theme-toggle></app-theme-toggle>
        </div>
        <a routerLink="/contact" (click)="closeMenu()" class="block w-full text-center btn-outline">
          Get Quote
        </a>
        <a routerLink="/contact" (click)="closeMenu()" class="block w-full text-center btn-primary">
          Book Consultation
        </a>
      </div>
    </div>
  </div>
</header>
